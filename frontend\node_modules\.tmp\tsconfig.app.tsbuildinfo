{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/signinoauthbuttons.tsx", "../../src/components/topbar.tsx", "../../src/components/skeletons/featuredgridskeleton.tsx", "../../src/components/skeletons/playlistskeleton.tsx", "../../src/components/skeletons/userslistskeleton.tsx", "../../src/components/ui/avatar.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/resizable.tsx", "../../src/components/ui/scroll-area.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/slider.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/tabs.tsx", "../../src/layout/mainlayout.tsx", "../../src/layout/components/audioplayer.tsx", "../../src/layout/components/friendsactivity.tsx", "../../src/layout/components/leftsidebar.tsx", "../../src/layout/components/playbackcontrols.tsx", "../../src/lib/axios.ts", "../../src/lib/utils.ts", "../../src/pages/404/notfoundpage.tsx", "../../src/pages/admin/adminpage.tsx", "../../src/pages/admin/components/addalbumdialog.tsx", "../../src/pages/admin/components/addsongdialog.tsx", "../../src/pages/admin/components/albumstabcontent.tsx", "../../src/pages/admin/components/albumstable.tsx", "../../src/pages/admin/components/dashboardstats.tsx", "../../src/pages/admin/components/header.tsx", "../../src/pages/admin/components/songstabcontent.tsx", "../../src/pages/admin/components/songstable.tsx", "../../src/pages/admin/components/statscard.tsx", "../../src/pages/album/albumpage.tsx", "../../src/pages/auth-callback/authcallbackpage.tsx", "../../src/pages/chat/chatpage.tsx", "../../src/pages/chat/components/chatheader.tsx", "../../src/pages/chat/components/messageinput.tsx", "../../src/pages/chat/components/userslist.tsx", "../../src/pages/home/<USER>", "../../src/pages/home/<USER>/featuredsection.tsx", "../../src/pages/home/<USER>/playbutton.tsx", "../../src/pages/home/<USER>/sectiongrid.tsx", "../../src/pages/home/<USER>/sectiongridskeleton.tsx", "../../src/providers/authprovider.tsx", "../../src/stores/useauthstore.ts", "../../src/stores/usechatstore.ts", "../../src/stores/usemusicstore.ts", "../../src/stores/useplayerstore.ts", "../../src/types/index.ts"], "version": "5.8.3"}