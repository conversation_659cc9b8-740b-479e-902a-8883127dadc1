{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo \"No build step required for Node.js\""}, "keywords": [], "type": "module", "author": "", "license": "ISC", "description": "", "dependencies": {"@clerk/express": "^1.7.10", "cloudinary": "^2.7.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-fileupload": "^1.5.2", "mongoose": "^8.16.4", "node-cron": "^4.2.1", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}